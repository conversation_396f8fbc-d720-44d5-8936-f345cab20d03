import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.RootNode;

public class TestArrowDestructuring {
    public static void main(String[] args) {
        String[] testCases = {
            // Test 1: Simple array destructuring (should work)
            "const [a, b] = [1, 2];",
            
            // Test 2: Regular function with array destructuring (should work)
            "function test([x, y]) { console.log(x, y); }",
            
            // Test 3: Simple arrow function (should work)
            "const fn = (x) => console.log(x);",
            
            // Test 4: Arrow function with array destructuring (problematic)
            "const fn = ([key, value]) => console.log(key, value);",
            
            // Test 5: The specific failing case
            "Object.entries({a: 1}).forEach(([key, value]) => console.log(key, value));"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            System.out.println("\n=== Test Case " + (i + 1) + " ===");
            System.out.println("Input: " + testCase);
            
            try {
                RootNode root = AstUtil.parseClosure(testCase, "test-case-" + (i + 1) + ".js");
                System.out.println("✓ Parsing successful!");
                System.out.println("Output: " + root.toSource());
            } catch (Exception e) {
                System.err.println("✗ Error: " + e.getMessage());
                if (e.getCause() != null) {
                    System.err.println("Cause: " + e.getCause().getMessage());
                }
            }
        }
    }
}
