import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.RootNode;

public class TestParsing {
    public static void main(String[] args) {
        // Test different variations to isolate the issue
        String[] testCases = {
            // Simple arrow function
            "const fn = () => console.log('hello');",

            // Arrow function with simple parameter
            "const fn = (x) => console.log(x);",

            // Arrow function with array destructuring (the problematic case)
            "Object.entries({a: 1}).forEach(([key, value]) => console.log(key, value));",

            // Regular function with array destructuring (should work)
            "function test([key, value]) { console.log(key, value); }",

            // Template literal
            "const msg = `Hello world`;",
        };

        for (int i = 0; i < testCases.length; i++) {
            String testCase = testCases[i];
            System.out.println("\n=== Test Case " + (i + 1) + " ===");
            System.out.println("Input: " + testCase);

            try {
                RootNode root = AstUtil.parseClosure(testCase, "test-case-" + (i + 1) + ".js");
                System.out.println("✓ Parsing successful!");
                System.out.println("Output: " + root.toSource());
            } catch (Exception e) {
                System.err.println("✗ Error: " + e.getMessage());
                if (e.getCause() != null) {
                    System.err.println("Cause: " + e.getCause().getMessage());
                }
            }
        }
    }
}
