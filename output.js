var $jscomp = $jscomp || {};
$jscomp.scope = {};
$jscomp.createTemplateTagFirstArg = function (a) {
  return (a.raw = a);
};
$jscomp.createTemplateTagFirstArgWithRaw = function (a, b) {
  a.raw = b;
  return a;
};
$jscomp.arrayIteratorImpl = function (a) {
  var b = 0;
  return function () {
    return b < a.length ? { done: !1, value: a[b++] } : { done: !0 };
  };
};
$jscomp.arrayIterator = function (a) {
  return { next: $jscomp.arrayIteratorImpl(a) };
};
$jscomp.makeIterator = function (a) {
  var b = "undefined" != typeof Symbol && Symbol.iterator && a[Symbol.iterator];
  return b ? b.call(a) : $jscomp.arrayIterator(a);
};
$jscomp.arrayFromIterator = function (a) {
  for (var b, c = []; !(b = a.next()).done; ) c.push(b.value);
  return c;
};
$jscomp.arrayFromIterable = function (a) {
  return a instanceof Array
    ? a
    : $jscomp.arrayFromIterator($jscomp.makeIterator(a));
};
$jscomp.owns = function (a, b) {
  return Object.prototype.hasOwnProperty.call(a, b);
};
$jscomp.ASSUME_ES5 = !1;
$jscomp.ASSUME_NO_NATIVE_MAP = !1;
$jscomp.ASSUME_NO_NATIVE_SET = !1;
$jscomp.SIMPLE_FROUND_POLYFILL = !1;
$jscomp.ISOLATE_POLYFILLS = !1;
$jscomp.FORCE_POLYFILL_PROMISE = !1;
$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION = !1;
$jscomp.defineProperty =
  $jscomp.ASSUME_ES5 || "function" == typeof Object.defineProperties
    ? Object.defineProperty
    : function (a, b, c) {
        if (a == Array.prototype || a == Object.prototype) return a;
        a[b] = c.value;
        return a;
      };
$jscomp.getGlobal = function (a) {
  a = [
    "object" == typeof globalThis && globalThis,
    a,
    "object" == typeof window && window,
    "object" == typeof self && self,
    "object" == typeof global && global,
  ];
  for (var b = 0; b < a.length; ++b) {
    var c = a[b];
    if (c && c.Math == Math) return c;
  }
  throw Error("Cannot find global object");
};
$jscomp.global = $jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE =
  "function" === typeof Symbol && "symbol" === typeof Symbol("x");
$jscomp.TRUST_ES6_POLYFILLS =
  !$jscomp.ISOLATE_POLYFILLS || $jscomp.IS_SYMBOL_NATIVE;
$jscomp.polyfills = {};
$jscomp.propertyToPolyfillSymbol = {};
$jscomp.POLYFILL_PREFIX = "$jscp$";
var $jscomp$lookupPolyfilledValue = function (a, b) {
  var c = $jscomp.propertyToPolyfillSymbol[b];
  if (null == c) return a[b];
  c = a[c];
  return void 0 !== c ? c : a[b];
};
$jscomp.polyfill = function (a, b, c, e) {
  b &&
    ($jscomp.ISOLATE_POLYFILLS
      ? $jscomp.polyfillIsolated(a, b, c, e)
      : $jscomp.polyfillUnisolated(a, b, c, e));
};
$jscomp.polyfillUnisolated = function (a, b, c, e) {
  c = $jscomp.global;
  a = a.split(".");
  for (e = 0; e < a.length - 1; e++) {
    var f = a[e];
    if (!(f in c)) return;
    c = c[f];
  }
  a = a[a.length - 1];
  e = c[a];
  b = b(e);
  b != e &&
    null != b &&
    $jscomp.defineProperty(c, a, { configurable: !0, writable: !0, value: b });
};
$jscomp.polyfillIsolated = function (a, b, c, e) {
  var f = a.split(".");
  a = 1 === f.length;
  e = f[0];
  e = !a && e in $jscomp.polyfills ? $jscomp.polyfills : $jscomp.global;
  for (var k = 0; k < f.length - 1; k++) {
    var d = f[k];
    if (!(d in e)) return;
    e = e[d];
  }
  f = f[f.length - 1];
  c = $jscomp.IS_SYMBOL_NATIVE && "es6" === c ? e[f] : null;
  b = b(c);
  null != b &&
    (a
      ? $jscomp.defineProperty($jscomp.polyfills, f, {
          configurable: !0,
          writable: !0,
          value: b,
        })
      : b !== c &&
        (void 0 === $jscomp.propertyToPolyfillSymbol[f] &&
          ((c = (1e9 * Math.random()) >>> 0),
          ($jscomp.propertyToPolyfillSymbol[f] = $jscomp.IS_SYMBOL_NATIVE
            ? $jscomp.global.Symbol(f)
            : $jscomp.POLYFILL_PREFIX + c + "$" + f)),
        $jscomp.defineProperty(e, $jscomp.propertyToPolyfillSymbol[f], {
          configurable: !0,
          writable: !0,
          value: b,
        })));
};
$jscomp.assign =
  $jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.assign
    ? Object.assign
    : function (a, b) {
        for (var c = 1; c < arguments.length; c++) {
          var e = arguments[c];
          if (e) for (var f in e) $jscomp.owns(e, f) && (a[f] = e[f]);
        }
        return a;
      };
$jscomp.polyfill(
  "Object.assign",
  function (a) {
    return a || $jscomp.assign;
  },
  "es6",
  "es3"
);
$jscomp.underscoreProtoCanBeSet = function () {
  var a = { a: !0 },
    b = {};
  try {
    return (b.__proto__ = a), b.a;
  } catch (c) {}
  return !1;
};
$jscomp.setPrototypeOf =
  $jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.setPrototypeOf
    ? Object.setPrototypeOf
    : $jscomp.underscoreProtoCanBeSet()
    ? function (a, b) {
        a.__proto__ = b;
        if (a.__proto__ !== b) throw new TypeError(a + " is not extensible");
        return a;
      }
    : null;
$jscomp.generator = {};
$jscomp.generator.ensureIteratorResultIsObject_ = function (a) {
  if (!(a instanceof Object))
    throw new TypeError("Iterator result " + a + " is not an object");
};
$jscomp.generator.Context = function () {
  this.isRunning_ = !1;
  this.yieldAllIterator_ = null;
  this.yieldResult = void 0;
  this.nextAddress = 1;
  this.finallyAddress_ = this.catchAddress_ = 0;
  this.finallyContexts_ = this.abruptCompletion_ = null;
};
$jscomp.generator.Context.prototype.start_ = function () {
  if (this.isRunning_) throw new TypeError("Generator is already running");
  this.isRunning_ = !0;
};
$jscomp.generator.Context.prototype.stop_ = function () {
  this.isRunning_ = !1;
};
$jscomp.generator.Context.prototype.jumpToErrorHandler_ = function () {
  this.nextAddress = this.catchAddress_ || this.finallyAddress_;
};
$jscomp.generator.Context.prototype.next_ = function (a) {
  this.yieldResult = a;
};
$jscomp.generator.Context.prototype.throw_ = function (a) {
  this.abruptCompletion_ = { exception: a, isException: !0 };
  this.jumpToErrorHandler_();
};
$jscomp.generator.Context.prototype.return = function (a) {
  this.abruptCompletion_ = { return: a };
  this.nextAddress = this.finallyAddress_;
};
$jscomp.generator.Context.prototype.jumpThroughFinallyBlocks = function (a) {
  this.abruptCompletion_ = { jumpTo: a };
  this.nextAddress = this.finallyAddress_;
};
$jscomp.generator.Context.prototype.yield = function (a, b) {
  this.nextAddress = b;
  return { value: a };
};
$jscomp.generator.Context.prototype.yieldAll = function (a, b) {
  a = $jscomp.makeIterator(a);
  var c = a.next();
  $jscomp.generator.ensureIteratorResultIsObject_(c);
  if (c.done) (this.yieldResult = c.value), (this.nextAddress = b);
  else return (this.yieldAllIterator_ = a), this.yield(c.value, b);
};
$jscomp.generator.Context.prototype.jumpTo = function (a) {
  this.nextAddress = a;
};
$jscomp.generator.Context.prototype.jumpToEnd = function () {
  this.nextAddress = 0;
};
$jscomp.generator.Context.prototype.setCatchFinallyBlocks = function (a, b) {
  this.catchAddress_ = a;
  void 0 != b && (this.finallyAddress_ = b);
};
$jscomp.generator.Context.prototype.setFinallyBlock = function (a) {
  this.catchAddress_ = 0;
  this.finallyAddress_ = a || 0;
};
$jscomp.generator.Context.prototype.leaveTryBlock = function (a, b) {
  this.nextAddress = a;
  this.catchAddress_ = b || 0;
};
$jscomp.generator.Context.prototype.enterCatchBlock = function (a) {
  this.catchAddress_ = a || 0;
  a = this.abruptCompletion_.exception;
  this.abruptCompletion_ = null;
  return a;
};
$jscomp.generator.Context.prototype.enterFinallyBlock = function (a, b, c) {
  c
    ? (this.finallyContexts_[c] = this.abruptCompletion_)
    : (this.finallyContexts_ = [this.abruptCompletion_]);
  this.catchAddress_ = a || 0;
  this.finallyAddress_ = b || 0;
};
$jscomp.generator.Context.prototype.leaveFinallyBlock = function (a, b) {
  b = this.finallyContexts_.splice(b || 0)[0];
  if ((b = this.abruptCompletion_ = this.abruptCompletion_ || b)) {
    if (b.isException) return this.jumpToErrorHandler_();
    void 0 != b.jumpTo && this.finallyAddress_ < b.jumpTo
      ? ((this.nextAddress = b.jumpTo), (this.abruptCompletion_ = null))
      : (this.nextAddress = this.finallyAddress_);
  } else this.nextAddress = a;
};
$jscomp.generator.Context.prototype.forIn = function (a) {
  return new $jscomp.generator.Context.PropertyIterator(a);
};
$jscomp.generator.Context.PropertyIterator = function (a) {
  this.object_ = a;
  this.properties_ = [];
  for (var b in a) this.properties_.push(b);
  this.properties_.reverse();
};
$jscomp.generator.Context.PropertyIterator.prototype.getNext = function () {
  for (; 0 < this.properties_.length; ) {
    var a = this.properties_.pop();
    if (a in this.object_) return a;
  }
  return null;
};
$jscomp.generator.Engine_ = function (a) {
  this.context_ = new $jscomp.generator.Context();
  this.program_ = a;
};
$jscomp.generator.Engine_.prototype.next_ = function (a) {
  this.context_.start_();
  if (this.context_.yieldAllIterator_)
    return this.yieldAllStep_(
      this.context_.yieldAllIterator_.next,
      a,
      this.context_.next_
    );
  this.context_.next_(a);
  return this.nextStep_();
};
$jscomp.generator.Engine_.prototype.return_ = function (a) {
  this.context_.start_();
  var b = this.context_.yieldAllIterator_;
  if (b)
    return this.yieldAllStep_(
      "return" in b
        ? b["return"]
        : function (c) {
            return { value: c, done: !0 };
          },
      a,
      this.context_.return
    );
  this.context_.return(a);
  return this.nextStep_();
};
$jscomp.generator.Engine_.prototype.throw_ = function (a) {
  this.context_.start_();
  if (this.context_.yieldAllIterator_)
    return this.yieldAllStep_(
      this.context_.yieldAllIterator_["throw"],
      a,
      this.context_.next_
    );
  this.context_.throw_(a);
  return this.nextStep_();
};
$jscomp.generator.Engine_.prototype.yieldAllStep_ = function (a, b, c) {
  try {
    var e = a.call(this.context_.yieldAllIterator_, b);
    $jscomp.generator.ensureIteratorResultIsObject_(e);
    if (!e.done) return this.context_.stop_(), e;
    var f = e.value;
  } catch (k) {
    return (
      (this.context_.yieldAllIterator_ = null),
      this.context_.throw_(k),
      this.nextStep_()
    );
  }
  this.context_.yieldAllIterator_ = null;
  c.call(this.context_, f);
  return this.nextStep_();
};
$jscomp.generator.Engine_.prototype.nextStep_ = function () {
  for (; this.context_.nextAddress; )
    try {
      var a = this.program_(this.context_);
      if (a) return this.context_.stop_(), { value: a.value, done: !1 };
    } catch (b) {
      (this.context_.yieldResult = void 0), this.context_.throw_(b);
    }
  this.context_.stop_();
  if (this.context_.abruptCompletion_) {
    a = this.context_.abruptCompletion_;
    this.context_.abruptCompletion_ = null;
    if (a.isException) throw a.exception;
    return { value: a.return, done: !0 };
  }
  return { value: void 0, done: !0 };
};
$jscomp.generator.Generator_ = function (a) {
  this.next = function (b) {
    return a.next_(b);
  };
  this.throw = function (b) {
    return a.throw_(b);
  };
  this.return = function (b) {
    return a.return_(b);
  };
  this[Symbol.iterator] = function () {
    return this;
  };
};
$jscomp.generator.createGenerator = function (a, b) {
  b = new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(b));
  $jscomp.setPrototypeOf &&
    a.prototype &&
    $jscomp.setPrototypeOf(b, a.prototype);
  return b;
};
$jscomp.asyncExecutePromiseGenerator = function (a) {
  function b(e) {
    return a.next(e);
  }
  function c(e) {
    return a.throw(e);
  }
  return new Promise(function (e, f) {
    function k(d) {
      d.done ? e(d.value) : Promise.resolve(d.value).then(b, c).then(k, f);
    }
    k(a.next());
  });
};
$jscomp.asyncExecutePromiseGeneratorFunction = function (a) {
  return $jscomp.asyncExecutePromiseGenerator(a());
};
$jscomp.asyncExecutePromiseGeneratorProgram = function (a) {
  return $jscomp.asyncExecutePromiseGenerator(
    new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(a))
  );
};
$jscomp.getRestArguments = function () {
  for (var a = Number(this), b = [], c = a; c < arguments.length; c++)
    b[c - a] = arguments[c];
  return b;
};
$jscomp.initSymbol = function () {};
$jscomp.polyfill(
  "Symbol",
  function (a) {
    if (a) return a;
    var b = function (k, d) {
      this.$jscomp$symbol$id_ = k;
      $jscomp.defineProperty(this, "description", {
        configurable: !0,
        writable: !0,
        value: d,
      });
    };
    b.prototype.toString = function () {
      return this.$jscomp$symbol$id_;
    };
    var c = "jscomp_symbol_" + ((1e9 * Math.random()) >>> 0) + "_",
      e = 0,
      f = function (k) {
        if (this instanceof f)
          throw new TypeError("Symbol is not a constructor");
        return new b(c + (k || "") + "_" + e++, k);
      };
    return f;
  },
  "es6",
  "es3"
);
$jscomp.polyfill(
  "Symbol.iterator",
  function (a) {
    if (a) return a;
    a = Symbol("Symbol.iterator");
    for (
      var b =
          "Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(
            " "
          ),
        c = 0;
      c < b.length;
      c++
    ) {
      var e = $jscomp.global[b[c]];
      "function" === typeof e &&
        "function" != typeof e.prototype[a] &&
        $jscomp.defineProperty(e.prototype, a, {
          configurable: !0,
          writable: !0,
          value: function () {
            return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this));
          },
        });
    }
    return a;
  },
  "es6",
  "es3"
);
$jscomp.iteratorPrototype = function (a) {
  a = { next: a };
  a[Symbol.iterator] = function () {
    return this;
  };
  return a;
};
$jscomp.polyfill(
  "Promise",
  function (a) {
    function b() {
      this.batch_ = null;
    }
    function c(d) {
      return d instanceof f
        ? d
        : new f(function (g, h) {
            g(d);
          });
    }
    if (
      a &&
      (!(
        $jscomp.FORCE_POLYFILL_PROMISE ||
        ($jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION &&
          "undefined" === typeof $jscomp.global.PromiseRejectionEvent)
      ) ||
        !$jscomp.global.Promise ||
        -1 === $jscomp.global.Promise.toString().indexOf("[native code]"))
    )
      return a;
    b.prototype.asyncExecute = function (d) {
      if (null == this.batch_) {
        this.batch_ = [];
        var g = this;
        this.asyncExecuteFunction(function () {
          g.executeBatch_();
        });
      }
      this.batch_.push(d);
    };
    var e = $jscomp.global.setTimeout;
    b.prototype.asyncExecuteFunction = function (d) {
      e(d, 0);
    };
    b.prototype.executeBatch_ = function () {
      for (; this.batch_ && this.batch_.length; ) {
        var d = this.batch_;
        this.batch_ = [];
        for (var g = 0; g < d.length; ++g) {
          var h = d[g];
          d[g] = null;
          try {
            h();
          } catch (l) {
            this.asyncThrow_(l);
          }
        }
      }
      this.batch_ = null;
    };
    b.prototype.asyncThrow_ = function (d) {
      this.asyncExecuteFunction(function () {
        throw d;
      });
    };
    var f = function (d) {
      this.state_ = 0;
      this.result_ = void 0;
      this.onSettledCallbacks_ = [];
      this.isRejectionHandled_ = !1;
      var g = this.createResolveAndReject_();
      try {
        d(g.resolve, g.reject);
      } catch (h) {
        g.reject(h);
      }
    };
    f.prototype.createResolveAndReject_ = function () {
      function d(l) {
        return function (m) {
          h || ((h = !0), l.call(g, m));
        };
      }
      var g = this,
        h = !1;
      return { resolve: d(this.resolveTo_), reject: d(this.reject_) };
    };
    f.prototype.resolveTo_ = function (d) {
      if (d === this)
        this.reject_(new TypeError("A Promise cannot resolve to itself"));
      else if (d instanceof f) this.settleSameAsPromise_(d);
      else {
        a: switch (typeof d) {
          case "object":
            var g = null != d;
            break a;
          case "function":
            g = !0;
            break a;
          default:
            g = !1;
        }
        g ? this.resolveToNonPromiseObj_(d) : this.fulfill_(d);
      }
    };
    f.prototype.resolveToNonPromiseObj_ = function (d) {
      var g = void 0;
      try {
        g = d.then;
      } catch (h) {
        this.reject_(h);
        return;
      }
      "function" == typeof g
        ? this.settleSameAsThenable_(g, d)
        : this.fulfill_(d);
    };
    f.prototype.reject_ = function (d) {
      this.settle_(2, d);
    };
    f.prototype.fulfill_ = function (d) {
      this.settle_(1, d);
    };
    f.prototype.settle_ = function (d, g) {
      if (0 != this.state_)
        throw Error(
          "Cannot settle(" +
            d +
            ", " +
            g +
            "): Promise already settled in state" +
            this.state_
        );
      this.state_ = d;
      this.result_ = g;
      2 === this.state_ && this.scheduleUnhandledRejectionCheck_();
      this.executeOnSettledCallbacks_();
    };
    f.prototype.scheduleUnhandledRejectionCheck_ = function () {
      var d = this;
      e(function () {
        if (d.notifyUnhandledRejection_()) {
          var g = $jscomp.global.console;
          "undefined" !== typeof g && g.error(d.result_);
        }
      }, 1);
    };
    f.prototype.notifyUnhandledRejection_ = function () {
      if (this.isRejectionHandled_) return !1;
      var d = $jscomp.global.CustomEvent,
        g = $jscomp.global.Event,
        h = $jscomp.global.dispatchEvent;
      if ("undefined" === typeof h) return !0;
      "function" === typeof d
        ? (d = new d("unhandledrejection", { cancelable: !0 }))
        : "function" === typeof g
        ? (d = new g("unhandledrejection", { cancelable: !0 }))
        : ((d = $jscomp.global.document.createEvent("CustomEvent")),
          d.initCustomEvent("unhandledrejection", !1, !0, d));
      d.promise = this;
      d.reason = this.result_;
      return h(d);
    };
    f.prototype.executeOnSettledCallbacks_ = function () {
      if (null != this.onSettledCallbacks_) {
        for (var d = 0; d < this.onSettledCallbacks_.length; ++d)
          k.asyncExecute(this.onSettledCallbacks_[d]);
        this.onSettledCallbacks_ = null;
      }
    };
    var k = new b();
    f.prototype.settleSameAsPromise_ = function (d) {
      var g = this.createResolveAndReject_();
      d.callWhenSettled_(g.resolve, g.reject);
    };
    f.prototype.settleSameAsThenable_ = function (d, g) {
      var h = this.createResolveAndReject_();
      try {
        d.call(g, h.resolve, h.reject);
      } catch (l) {
        h.reject(l);
      }
    };
    f.prototype.then = function (d, g) {
      function h(n, p) {
        return "function" == typeof n
          ? function (q) {
              try {
                l(n(q));
              } catch (r) {
                m(r);
              }
            }
          : p;
      }
      var l,
        m,
        t = new f(function (n, p) {
          l = n;
          m = p;
        });
      this.callWhenSettled_(h(d, l), h(g, m));
      return t;
    };
    f.prototype.catch = function (d) {
      return this.then(void 0, d);
    };
    f.prototype.callWhenSettled_ = function (d, g) {
      function h() {
        switch (l.state_) {
          case 1:
            d(l.result_);
            break;
          case 2:
            g(l.result_);
            break;
          default:
            throw Error("Unexpected state: " + l.state_);
        }
      }
      var l = this;
      null == this.onSettledCallbacks_
        ? k.asyncExecute(h)
        : this.onSettledCallbacks_.push(h);
      this.isRejectionHandled_ = !0;
    };
    f.resolve = c;
    f.reject = function (d) {
      return new f(function (g, h) {
        h(d);
      });
    };
    f.race = function (d) {
      return new f(function (g, h) {
        for (
          var l = $jscomp.makeIterator(d), m = l.next();
          !m.done;
          m = l.next()
        )
          c(m.value).callWhenSettled_(g, h);
      });
    };
    f.all = function (d) {
      var g = $jscomp.makeIterator(d),
        h = g.next();
      return h.done
        ? c([])
        : new f(function (l, m) {
            function t(q) {
              return function (r) {
                n[q] = r;
                p--;
                0 == p && l(n);
              };
            }
            var n = [],
              p = 0;
            do
              n.push(void 0),
                p++,
                c(h.value).callWhenSettled_(t(n.length - 1), m),
                (h = g.next());
            while (!h.done);
          });
    };
    return f;
  },
  "es6",
  "es3"
);
$jscomp.polyfill(
  "Object.entries",
  function (a) {
    return a
      ? a
      : function (b) {
          var c = [],
            e;
          for (e in b) $jscomp.owns(b, e) && c.push([e, b[e]]);
          return c;
        };
  },
  "es8",
  "es3"
);
Ext.define("MyAppName.Application", {
  extend: "Ext.app.Application",
  name: "MyAppName",
  quickTips: !1,
  platformConfig: { desktop: { quickTips: !0 } },
  testArrays: [1, 2, 3, "test", !0, null],
  config: {
    debug: !0,
    version: "1.0.0",
    features: ["grid", "forms", "charts"],
  },
  launch: function (a) {
    console.log("Application launched with profile:", a);
    a && "development" === a.name && this.enableDebugMode();
    try {
      var b = this.createViewport();
    } catch (h) {
      console.error("Failed to create viewport:", h), this.handleError(h);
    } finally {
      this.initializeComplete = !0;
    }
    for (var c = 0; c < this.testArrays.length; c++)
      console.log("Array item:", this.testArrays[c]);
    for (var e in this.config)
      console.log("Config key:", e, "value:", this.config[e]);
    for (c = 0; 5 > c; ) c++;
    do console.log("Do-while iteration:", c), c--;
    while (0 < c);
    var f;
    c = null == (f = { myProperty: "hello" }) ? void 0 : f.myProperty.big();
    console.log(c);
    switch (null == a ? void 0 : a.name) {
      case "development":
        var k;
        null == this || null == (k = this.setLogLevel) || k.call(this, "debug");
        break;
      case "production":
        var d;
        null == this || null == (d = this.setLogLevel) || d.call(this, "error");
        break;
      default:
        var g;
        null == this || null == (g = this.setLogLevel) || g.call(this, "info");
    }
    return b;
  },
  processData: function () {
    var a = $jscomp.makeIterator(this.testArrays),
      b = a.next().value,
      c = a.next().value;
    a = $jscomp.arrayFromIterator(a);
    var e = this.config,
      f = e.debug;
    e = e.version;
    var k = this.testArrays
        .filter(function (h) {
          return null !== h;
        })
        .map(function (h) {
          return 2 * h;
        })
        .reduce(function (h, l) {
          return h + l;
        }, 0),
      d = "Processing complete. Result: " + k + ", Version: " + e,
      g = [].concat($jscomp.arrayFromIterable(this.testArrays), [4, 5, 6]);
    return {
      first: b,
      second: c,
      rest: a,
      debug: f,
      version: e,
      doubled: k,
      message: d,
      newArray: g,
    };
  },
  loadData: function () {
    var a, b, c;
    return $jscomp.asyncExecutePromiseGeneratorProgram(function (e) {
      switch (e.nextAddress) {
        case 1:
          return e.setCatchFinallyBlocks(2), e.yield(fetch("/api/data"), 4);
        case 4:
          return (a = e.yieldResult), e.yield(a.json(), 5);
        case 5:
          return (b = e.yieldResult), e.return(b);
        case 2:
          throw (
            ((c = e.enterCatchBlock()),
            Error("Data loading failed: " + c.message))
          );
      }
    });
  },
  dataGenerator: function $jscomp$generator$function() {
    var b = this,
      c,
      e,
      f;
    return $jscomp.generator.createGenerator(
      $jscomp$generator$function,
      function (k) {
        1 == k.nextAddress &&
          ((c = $jscomp.makeIterator(b.testArrays)), (e = c.next()));
        if (3 != k.nextAddress) {
          if (e.done) return k.yieldAll(b.config.features, 0);
          f = e.value;
          return k.yield(2 * f, 3);
        }
        e = c.next();
        return k.jumpTo(2);
      }
    );
  },
  createUtilityClass: function () {
    var a = function (b) {
      this.data = b;
    };
    a.prototype.processData = function () {
      return this.data.map(function (b) {
        return b.toString();
      });
    };
    $jscomp.global.Object.defineProperties(a.prototype, {
      length: {
        configurable: !0,
        enumerable: !0,
        get: function () {
          return this.data.length;
        },
        set: function (b) {
          this.data.length = b;
        },
      },
    });
    return a;
  },
  createViewport: function () {
    return new Ext.container.Viewport({
      layout: "border",
      items: [
        Object.assign({}, { region: "north" }, this.getToolbarConfig(), {
          height: 50,
        }),
        {
          region: "center",
          xtype: "panel",
          html: (null == this || this.incrementCounter(), this.getMessage()),
        },
      ],
    });
  },
  getMessage: function (a, b) {
    return (
      (void 0 === a ? "Default message" : a) +
      " from ExtJS application" +
      (void 0 === b ? "!" : b)
    );
  },
  combineMessages: function () {
    var a = { name: "Gary", age: 28 },
      b = a.name,
      c = a.age;
    a = void 0 === a.company ? "Eastland" : a.company;
    var e,
      f = null == (e = { myProperty: "hello" }) ? void 0 : e.myProperty.big();
    console.log(f);
    Object.entries({ one: 1, two: 2 }).forEach(function (k) {
      var d = $jscomp.makeIterator(k);
      k = d.next().value;
      d = d.next().value;
      console.log("key: " + k + ", value: " + d);
    });
    console.log(b, c, a);
  },
  enableDebugMode: function () {
    this.debugEnabled = !this.debugEnabled;
    delete this.temporaryData;
  },
  incrementCounter: function () {
    this.counter = this.counter || 0;
    return ++this.counter;
  },
  getLogLevel: function () {
    return this.debugEnabled ? "debug" : "info";
  },
  processLoop: function () {
    var a = 0;
    a: for (; 3 > a; a++) {
      var b = 0;
      for (; 3 > b; b++) if (1 === a && 1 === b) break a;
    }
  },
  handleError: function (a) {
    if (!a.handled) throw Error("Unhandled application error: " + a.message);
  },
  debugPoint: function () {
    debugger;
  },
  emptyOperation: function () {},
  bindContext: function () {
    var a = this;
    return function () {
      return a.name;
    };
  },
  callParent: function () {
    this.callParent(arguments);
  },
  setupModules: function () {
    return { utils: this.getUtilities(), config: this.config };
  },
  safeAccess: function (a) {
    var b, c, e;
    return null == a
      ? void 0
      : null == (b = a.data)
      ? void 0
      : null == (c = b.items)
      ? void 0
      : null == (e = c[0])
      ? void 0
      : e.name();
  },
  getValueWithDefault: function (a) {
    return null != a ? a : "default value";
  },
  literals: {
    string: "test string",
    number: 42,
    boolean: !0,
    null: null,
    undefined: void 0,
    regex: /test\d+/gi,
  },
  getToolbarConfig: function () {
    return {
      xtype: "toolbar",
      dock: "top",
      items: [
        "->",
        { text: "Test Button", handler: this.onTestClick, scope: this },
      ],
    };
  },
  onTestClick: function () {
    var a = this.processData();
    console.log("Test result:", a);
    a = this.dataGenerator();
    console.log("Generator result:", a.next().value);
    this.loadData()
      .then(function (b) {
        console.log("Async data loaded:", b);
      })
      .catch(function (b) {
        console.error("Async error:", b);
      });
  },
  getUtilities: function () {
    return {
      format: function (a) {
        var b = $jscomp.getRestArguments.apply(1, arguments);
        return a.replace(/{(\d+)}/g, function (c, e) {
          return void 0 !== b[e] ? b[e] : c;
        });
      },
    };
  },
});
