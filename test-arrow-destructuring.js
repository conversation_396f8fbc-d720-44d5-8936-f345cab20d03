// Test cases for array destructuring in different contexts

// 1. Array destructuring in variable declaration (should work)
const [a, b] = [1, 2];

// 2. Array destructuring in regular function parameters (should work)
function test([x, y]) {
    console.log(x, y);
}

// 3. Array destructuring in arrow function parameters (problematic)
const fn = ([key, value]) => {
    console.log(key, value);
};

// 4. The specific case that's failing
Object.entries({a: 1, b: 2}).forEach(([key, value]) => {
    console.log(`key: ${key}, value: ${value}`);
});
